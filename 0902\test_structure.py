"""
测试PyTorch RT预测系统的基本结构
不需要安装额外依赖包
"""

import os
import sys

def test_file_structure():
    """测试文件结构是否完整"""
    print("=== 测试文件结构 ===")
    
    required_files = [
        "README.md",
        "requirements.txt", 
        "rt_prediction_model_pytorch.py",
        "sprs_rt_pytorch.py",
        "data_analysis_pytorch.py",
        "mysql_config.json",
        "create_rt_table.sql",
        "USAGE_EXAMPLES.md"
    ]
    
    base_dir = "0902"
    missing_files = []
    
    for file in required_files:
        file_path = os.path.join(base_dir, file)
        if os.path.exists(file_path):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n缺少文件: {missing_files}")
        return False
    else:
        print("\n所有必需文件都存在")
        return True

def test_directory_structure():
    """测试目录结构"""
    print("\n=== 测试目录结构 ===")
    
    required_dirs = [
        "outputs",
        "outputs/data",
        "outputs/models", 
        "outputs/plots",
        "outputs/predictions"
    ]
    
    base_dir = "0902"
    missing_dirs = []
    
    for dir_name in required_dirs:
        dir_path = os.path.join(base_dir, dir_name)
        if os.path.exists(dir_path):
            print(f"✅ {dir_name}/")
        else:
            print(f"❌ {dir_name}/")
            missing_dirs.append(dir_name)
    
    if missing_dirs:
        print(f"\n缺少目录: {missing_dirs}")
        return False
    else:
        print("\n所有必需目录都存在")
        return True

def test_file_contents():
    """测试文件内容基本结构"""
    print("\n=== 测试文件内容 ===")
    
    base_dir = "0902"
    
    # 测试Python文件是否包含基本类和函数
    python_files = {
        "rt_prediction_model_pytorch.py": ["class RTNet", "class RTPredictor", "def train", "def predict"],
        "sprs_rt_pytorch.py": ["def load_your_data", "def main", "RTPredictor"],
        "data_analysis_pytorch.py": ["class RTDataAnalyzer", "def analyze_rt_data"]
    }
    
    for file_name, expected_content in python_files.items():
        file_path = os.path.join(base_dir, file_name)
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n检查 {file_name}:")
            for item in expected_content:
                if item in content:
                    print(f"  ✅ {item}")
                else:
                    print(f"  ❌ {item}")
        except Exception as e:
            print(f"  ❌ 读取文件失败: {e}")

def test_config_files():
    """测试配置文件"""
    print("\n=== 测试配置文件 ===")
    
    base_dir = "0902"
    
    # 测试JSON配置文件
    json_file = os.path.join(base_dir, "mysql_config.json")
    try:
        import json
        with open(json_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        required_keys = ["host", "port", "user", "password", "database", "table_name"]
        print(f"检查 mysql_config.json:")
        for key in required_keys:
            if key in config:
                print(f"  ✅ {key}")
            else:
                print(f"  ❌ {key}")
    except Exception as e:
        print(f"  ❌ JSON配置文件错误: {e}")
    
    # 测试SQL文件
    sql_file = os.path.join(base_dir, "create_rt_table.sql")
    try:
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        sql_keywords = ["CREATE TABLE", "sprs_rt_data", "rt_prediction_results", "INSERT INTO"]
        print(f"\n检查 create_rt_table.sql:")
        for keyword in sql_keywords:
            if keyword in sql_content:
                print(f"  ✅ {keyword}")
            else:
                print(f"  ❌ {keyword}")
    except Exception as e:
        print(f"  ❌ SQL文件错误: {e}")

def compare_with_tensorflow_version():
    """与TensorFlow版本进行对比"""
    print("\n=== 与TensorFlow版本对比 ===")
    
    tf_dir = "0826"
    pytorch_dir = "0902"
    
    # 检查对应文件是否存在
    file_mapping = {
        "rt_prediction_model.py": "rt_prediction_model_pytorch.py",
        "sprs_rt.py": "sprs_rt_pytorch.py", 
        "data_analysis.py": "data_analysis_pytorch.py"
    }
    
    for tf_file, pytorch_file in file_mapping.items():
        tf_path = os.path.join(tf_dir, tf_file)
        pytorch_path = os.path.join(pytorch_dir, pytorch_file)
        
        tf_exists = os.path.exists(tf_path)
        pytorch_exists = os.path.exists(pytorch_path)
        
        print(f"{tf_file} -> {pytorch_file}")
        print(f"  TensorFlow版本: {'✅' if tf_exists else '❌'}")
        print(f"  PyTorch版本: {'✅' if pytorch_exists else '❌'}")

def main():
    """主测试函数"""
    print("PyTorch RT预测系统结构测试")
    print("=" * 50)
    
    # 检查当前工作目录
    current_dir = os.getcwd()
    print(f"当前工作目录: {current_dir}")
    
    # 检查0902目录是否存在
    if not os.path.exists("0902"):
        print("❌ 0902目录不存在")
        return
    
    # 执行各项测试
    tests = [
        test_file_structure,
        test_directory_structure, 
        test_file_contents,
        test_config_files,
        compare_with_tensorflow_version
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"测试失败: {e}")
            results.append(False)
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结:")
    passed = sum(1 for r in results if r is True)
    total = len([r for r in results if r is not None])
    
    if total > 0:
        print(f"通过测试: {passed}/{total}")
    
    if all(r is not False for r in results):
        print("🎉 PyTorch RT预测系统结构完整！")
        print("\n下一步:")
        print("1. 安装依赖包: pip install -r 0902/requirements.txt")
        print("2. 运行主程序: python 0902/sprs_rt_pytorch.py")
        print("3. 或运行模型测试: python 0902/rt_prediction_model_pytorch.py")
    else:
        print("⚠️ 发现一些问题，请检查上述输出")

if __name__ == "__main__":
    main()
