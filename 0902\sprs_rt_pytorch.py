"""
RT预测模型使用示例 - PyTorch版本
演示如何使用RTPredictor进行训练和预测
"""

import pandas as pd
import numpy as np
from rt_prediction_model_pytorch import RTPredictor, get_output_path, generate_sample_data
from data_analysis_pytorch import RTDataAnalyzer
import pymysql
from sqlalchemy import create_engine, text, Engine
import os
from typing import Optional, Dict, Any, List
import json
from datetime import datetime

def save_results_to_mysql(
    results_df: pd.DataFrame,
    table_name: str = "rt_prediction_results",
    config_file: Optional[str] = None,
    **kwargs
) -> bool:
    """
    将预测结果保存到MySQL数据库（使用UPSERT逻辑）
    如果相同条件组合已存在，则更新；否则插入新记录

    Args:
        results_df: 包含预测结果的DataFrame
        table_name: 结果表名
        config_file: MySQL配置文件路径
        **kwargs: 其他数据库连接参数

    Returns:
        是否保存成功
    """
    try:
        print(f"\n💾 保存/更新预测结果到MySQL表: {table_name}")

        # 获取数据库连接参数
        if config_file and os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            host = config.get('host', 'localhost')
            port = config.get('port', 3306)
            user = config.get('user', 'root')
            password = config.get('password', '')
            database = config.get('database', 'gtxsemi')
        else:
            host = kwargs.get('host', 'localhost')
            port = kwargs.get('port', 3306)
            user = kwargs.get('user', 'root')
            password = kwargs.get('password', '')
            database = kwargs.get('database', 'gtxsemi')

        # 创建数据库连接
        connection_string = f"mysql+pymysql://{user}:{password}@{host}:{port}/{database}"
        engine = create_engine(connection_string)

        # 添加时间戳
        if 'timestamp' not in results_df.columns:
            results_df['timestamp'] = datetime.now()

        # 保存到MySQL
        results_df.to_sql(
            table_name,
            con=engine,
            if_exists='append',
            index=False,
            method='multi'
        )

        print(f"✅ 成功保存 {len(results_df)} 条记录到MySQL表 {table_name}")
        return True

    except Exception as e:
        print(f"❌ 保存到MySQL失败: {e}")
        return False

def load_your_data(
    data_source: str = "simulated",
    config_file: Optional[str] = None,
    csv_file_path: Optional[str] = None,
    **kwargs
) -> pd.DataFrame:
    """
    从不同数据源加载数据

    Args:
        data_source: 数据源类型 ("mysql", "csv", "simulated")
        config_file: MySQL配置文件路径
        csv_file_path: CSV文件路径
        **kwargs: 其他参数

    Returns:
        加载的数据DataFrame
    """
    if data_source == "mysql":
        return load_mysql_data(config_file, **kwargs)
    elif data_source == "csv":
        return load_csv_data(csv_file_path, **kwargs)
    elif data_source == "simulated":
        return generate_sample_data(kwargs.get('n_samples', 1000))
    else:
        raise ValueError(f"不支持的数据源类型: {data_source}")

def load_mysql_data(config_file: Optional[str] = None, **kwargs) -> pd.DataFrame:
    """从MySQL数据库加载数据"""
    try:
        # 获取数据库连接参数
        if config_file and os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
        else:
            config = kwargs

        host = config.get('host', 'localhost')
        port = config.get('port', 3306)
        user = config.get('user', 'root')
        password = config.get('password', '')
        database = config.get('database', 'gtxsemi')
        table_name = config.get('table_name', 'sprs_rt_data')

        # 创建数据库连接
        connection_string = f"mysql+pymysql://{user}:{password}@{host}:{port}/{database}"
        engine = create_engine(connection_string)

        # 查询数据
        query = f"SELECT * FROM {table_name}"
        df = pd.read_sql(query, engine)

        print(f"✅ 从MySQL加载了 {len(df)} 条记录")
        return df

    except Exception as e:
        print(f"❌ 从MySQL加载数据失败: {e}")
        raise

def load_csv_data(csv_file_path: str, **kwargs) -> pd.DataFrame:
    """从CSV文件加载数据"""
    try:
        if not csv_file_path or not os.path.exists(csv_file_path):
            raise FileNotFoundError(f"CSV文件不存在: {csv_file_path}")

        df = pd.read_csv(csv_file_path)
        print(f"✅ 从CSV文件加载了 {len(df)} 条记录")
        return df

    except Exception as e:
        print(f"❌ 从CSV加载数据失败: {e}")
        raise

def normalize_column_names(df: pd.DataFrame) -> pd.DataFrame:
    """标准化列名，支持不同大小写格式"""
    column_mapping = {
        'EQUIP': 'equip', 'Equip': 'equip',
        'SUB_EQUIP': 'sub_equip', 'Sub_Equip': 'sub_equip',
        'CAPABILITY': 'capability', 'Capability': 'capability',
        'RECIPE': 'recipe', 'Recipe': 'recipe',
        'QTY': 'qty', 'Qty': 'qty',
        'RT': 'rt', 'Rt': 'rt'
    }
    
    df_normalized = df.copy()
    df_normalized.columns = [column_mapping.get(col, col.lower()) for col in df.columns]
    
    return df_normalized

def validate_data(df: pd.DataFrame) -> pd.DataFrame:
    """验证和清理数据"""
    print("🔍 验证数据质量...")
    
    # 标准化列名
    df = normalize_column_names(df)
    
    # 检查必需的列
    required_columns = ['equip', 'sub_equip', 'capability', 'recipe', 'qty', 'rt']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        raise ValueError(f"缺少必需的列: {missing_columns}")
    
    # 删除缺失值
    initial_count = len(df)
    df = df.dropna(subset=required_columns)
    if len(df) < initial_count:
        print(f"⚠️ 删除了 {initial_count - len(df)} 行包含缺失值的数据")
    
    # 数据类型转换
    df['qty'] = pd.to_numeric(df['qty'], errors='coerce')
    df['rt'] = pd.to_numeric(df['rt'], errors='coerce')
    
    # 删除转换失败的行
    df = df.dropna(subset=['qty', 'rt'])
    
    # 删除异常值
    df = df[(df['qty'] > 0) & (df['rt'] > 0)]
    df = df[df['qty'] <= 1000]  # 合理的数量上限
    df = df[df['rt'] <= 1000]   # 合理的时间上限
    
    print(f"✅ 数据验证完成，有效数据: {len(df)} 条")
    return df

def handle_duplicate_data(df: pd.DataFrame) -> pd.DataFrame:
    """处理重复数据，对相同条件组合取中位数"""
    print("🔄 处理重复数据...")
    
    condition_columns = ['equip', 'sub_equip', 'capability', 'recipe', 'qty']
    
    # 检查重复
    duplicates = df.duplicated(subset=condition_columns, keep=False)
    if duplicates.sum() > 0:
        print(f"发现 {duplicates.sum()} 条重复数据")
        
        # 对重复数据取中位数
        df_clean = df.groupby(condition_columns)['rt'].median().reset_index()
        
        # 重新添加其他可能的列
        for col in df.columns:
            if col not in df_clean.columns and col != 'rt':
                df_clean[col] = df.groupby(condition_columns)[col].first().values
        
        print(f"处理后数据: {len(df_clean)} 条")
        return df_clean
    else:
        print("未发现重复数据")
        return df

def main():
    """主函数：演示完整的训练和预测流程"""
    print("=== PyTorch RT预测系统 ===")
    
    # 1. 数据源选择
    print("\n请选择数据源:")
    print("1. 模拟数据")
    print("2. MySQL数据库")
    print("3. CSV文件")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == "1":
        print("使用模拟数据...")
        df = load_your_data("simulated", n_samples=1000)
    elif choice == "2":
        config_file = "mysql_config.json"
        if not os.path.exists(config_file):
            print(f"MySQL配置文件 {config_file} 不存在，请先创建配置文件")
            return
        df = load_your_data("mysql", config_file=config_file)
    elif choice == "3":
        csv_path = input("请输入CSV文件路径: ").strip()
        df = load_your_data("csv", csv_file_path=csv_path)
    else:
        print("无效选择")
        return
    
    # 2. 数据预处理
    print("\n2. 数据预处理...")
    df = validate_data(df)
    df = handle_duplicate_data(df)
    
    # 3. 数据分析
    print("\n3. 数据分析...")
    analyzer = RTDataAnalyzer()
    analyzer.analyze_rt_data(df, save_plots=True, output_dir="outputs/plots")
    
    # 4. 模型训练
    print("\n4. 开始模型训练...")
    predictor = RTPredictor()
    
    # 根据数据量调整训练参数
    epochs = 100 if len(df) > 1000 else 50
    batch_size = 32 if len(df) > 500 else 16
    
    results = predictor.train(
        df,
        test_size=0.2,
        validation_split=0.2,
        epochs=epochs,
        batch_size=batch_size
    )
    
    # 5. 模型评估
    print("\n5. 模型评估结果:")
    print(f"平均绝对误差 (MAE): {results['mae']:.4f}")
    print(f"均方根误差 (RMSE): {results['rmse']:.4f}")
    print(f"决定系数 (R²): {results['r2']:.4f}")
    
    # 绘制训练历史
    predictor.plot_training_history()
    
    # 6. 预测示例
    print("\n6. 预测示例:")
    test_cases = pd.DataFrame([
        {'equip': 'EQ001', 'sub_equip': 'SUB_A', 'capability': 'CAP_HIGH', 'recipe': 'RECIPE_1', 'qty': 15},
        {'equip': 'EQ002', 'sub_equip': 'SUB_B', 'capability': 'CAP_MED', 'recipe': 'RECIPE_2', 'qty': 20},
        {'equip': 'EQ003', 'sub_equip': 'SUB_C', 'capability': 'CAP_LOW', 'recipe': 'RECIPE_3', 'qty': 10}
    ])
    
    predictions = predictor.predict(test_cases)
    
    print("\n预测结果:")
    print("=" * 80)
    for i, (_, row) in enumerate(test_cases.iterrows()):
        print(f"案例 {i+1}: {row['equip']}-{row['sub_equip']}-{row['capability']}-{row['recipe']}, qty={row['qty']}")
        print(f"  预测RT: {predictions[i]:.2f} 小时")
    
    # 7. 保存模型
    print("\n7. 保存模型...")
    model_path = predictor.save_model("rt_prediction_model_pytorch")
    print(f"模型已保存到: {model_path}")
    
    print("\n=== 训练完成 ===")
    return predictor, results

if __name__ == "__main__":
    main()
